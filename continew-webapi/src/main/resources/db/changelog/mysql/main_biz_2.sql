-- liquibase formatted sql

-- changeset yqx:10000
-- comment 客户新增用户名密码
ALTER TABLE `biz_customer`
    ADD COLUMN `username` varchar(255) NULL COMMENT '用户名' AFTER `product_name`,
    ADD COLUMN `password` varchar(255) NULL COMMENT '密码' AFTER `username`;

-- changeset yqx:10001
-- comment 新增客户邮箱
ALTER TABLE `biz_customer_requirement`
    ADD COLUMN `customer_email` varchar(255) NULL COMMENT '客户邮箱' AFTER `ad_account_name`;
ALTER TABLE `biz_ad_account_order`
    ADD COLUMN `customer_email` varchar(255) NULL COMMENT '客户邮箱' AFTER `is_one_dollar`;
-- changeset yqx:10002
-- comment 接收状态
ALTER TABLE `biz_ad_account_order`
    ADD COLUMN `take_status` bit(1) NOT NULL DEFAULT b'0' COMMENT '接收状态' AFTER `customer_email`;

-- changeset yqx:10003
-- comment 新建表
CREATE TABLE `biz_customer_order_group`
(
    `id`          bigint NOT NULL COMMENT 'ID',
    `name`        varchar(255) DEFAULT NULL COMMENT '名称',
    `create_time` datetime     DEFAULT NULL COMMENT '创建时间',
    `customer_id` bigint       DEFAULT NULL COMMENT '客户ID',
    PRIMARY KEY (`id`)
) ENGINE = InnoDB COMMENT ='客户下户订单分组';
ALTER TABLE `biz_ad_account_order`
    ADD COLUMN `group_id` bigint NULL COMMENT '客户分组ID' AFTER `take_status`;


-- changeset hans:10001
-- comment 新建表
alter table biz_ad_account_order
    add clear_status int NOT NULL DEFAULT 1 comment '清零状态';

alter table biz_ad_account_order
    add clear_time datetime null comment '清零时间';

alter table biz_clear_order
    add ad_account_order_id bigint null comment '关联下户订单';


-- changeset hans:10002
-- comment 新增字段
alter table biz_purchase_order
    add receive_date date null comment '验收时间' after receive_price;


-- changeset yqx:10004
-- comment 新增字段
ALTER TABLE `sys_user`
    ADD COLUMN `job_rank` int NULL COMMENT '岗位职级',
    ADD COLUMN `telegram_id` int NULL COMMENT '飞机号';


-- changeset yqx:10005
-- comment 新增字段
ALTER TABLE `biz_customer`
    ADD COLUMN `cooperate_time` datetime NULL COMMENT '合作时间';

-- changeset yqx:10006
-- comment 新增字段
CREATE TABLE `biz_sales_daily_summary`
(
    `id`          BIGINT   NOT NULL COMMENT 'ID',
    `record_date` DATE     NOT NULL COMMENT '日报记录的日期',
    `content`     TEXT     NOT NULL COMMENT '日报具体内容，支持长文本',
    `create_time` datetime NOT NULL COMMENT '创建时间',
    `create_user` bigint   NOT NULL COMMENT '创建人',
    `update_time` datetime NULL COMMENT '更新时间',
    PRIMARY KEY (`id`)
) ENGINE = InnoDB COMMENT ='商务日报';
ALTER TABLE `sys_user`
    MODIFY COLUMN `telegram_id` varchar(100) NULL DEFAULT NULL COMMENT '飞机号' AFTER `job_rank`;

-- changeset yqx:10007
-- comment 新增表
CREATE TABLE `biz_sales_schedule`
(
    `id`            BIGINT   NOT NULL COMMENT 'ID',
    `sales_id`      BIGINT   NOT NULL COMMENT '商务人员ID',
    `schedule_date` DATE     NOT NULL COMMENT '排班/请假的具体日期',
    `schedule_type` TINYINT  NOT NULL COMMENT '类型: 1=请假, 2=排班',
    `create_time`   DATETIME NOT NULL COMMENT '创建时间',
    `update_time`   DATETIME NULL COMMENT '更新时间',
    PRIMARY KEY (`id`)
) ENGINE = InnoDB COMMENT ='商务人员排班/请假记录表';


-- changeset yyh:10001
-- comment 广告组表增加事件类型和优化目标
alter table biz_fb_ad_sets add custom_event_type varchar(200) comment '自定义事件类型';
alter table biz_fb_ad_sets add optimization_goal varchar(200) comment '优化目标';

-- changeset yyh:10002
-- comment 修改广告系列成效表结构
ALTER TABLE `biz_campaign_insight`
    CHANGE COLUMN `platform_ad_id` `ad_account_id` varchar(64) NOT NULL COMMENT '广告户ID',
    ADD COLUMN `platform_adset_id` varchar(64) NOT NULL COMMENT '广告组ID' AFTER `platform_campaign_id`,
    ADD COLUMN `impressions` int DEFAULT 0 COMMENT '展示次数' AFTER `stat_date`,
    ADD COLUMN `clicks` int DEFAULT 0 COMMENT '点击量' AFTER `impressions`,
    ADD COLUMN `actions_data` json NULL COMMENT '成效数据JSON' AFTER `spend`,
    ADD COLUMN `update_time` datetime NULL COMMENT '更新时间' AFTER `create_time`,
    DROP INDEX `unique_campaign_ad_stat`,
    ADD UNIQUE INDEX `unique_campaign_adset_stat` (`platform_campaign_id`, `platform_adset_id`, `stat_date`);

-- changeset yyh:10003
-- comment 设置广告系列成效表ID为自增
ALTER TABLE `biz_campaign_insight` MODIFY COLUMN `id` bigint NOT NULL AUTO_INCREMENT COMMENT 'ID';

-- changeset yyh:10004
-- comment 成效表增加成效字段
ALTER TABLE `biz_campaign_insight` ADD COLUMN `conversion_value` decimal(10,2) DEFAULT 0 COMMENT '转化成效值';
ALTER TABLE `biz_campaign_insight` ADD COLUMN `conversion_key` varchar(100) DEFAULT NULL COMMENT '成效指标key值';

-- changeset yyh:10005
-- comment 成效表增加conversions数据字段
ALTER TABLE `biz_campaign_insight` ADD COLUMN `conversions_data` json NULL COMMENT 'conversions成效数据JSON';

-- changeset yyh:10006
-- comment 成效表增加actions数据字段
ALTER TABLE biz_fb_ad_sets  CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE biz_fb_ad  CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE biz_fb_ad_campaigns  CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- changeset hans:10003
-- comment 新增字段
alter table biz_card
    add ad_platform int default 1 not null comment '广告平台';

alter table biz_ad_account_card
    add ad_platform int default 1 not null comment '广告平台';

alter table biz_card_transaction
    add ad_platform int default 1 not null comment '广告平台';

-- changeset yqx:10008
-- comment 新增表
CREATE TABLE `biz_customer_business_user`
(
    `id`          BIGINT AUTO_INCREMENT COMMENT 'ID',
    `customer_id` BIGINT   NOT NULL COMMENT '客户ID，用于关联客户信息表',
    `user_id`     BIGINT   NOT NULL COMMENT '关联的商务用户ID',
    `create_time` DATETIME NOT NULL COMMENT '创建时间',
    PRIMARY KEY (`id`)

) ENGINE = InnoDB COMMENT ='客户商务对接表';
ALTER TABLE biz_ad_account_insight ADD COLUMN business_user_id BIGINT COMMENT '关联商务';
ALTER TABLE biz_ad_account_order ADD COLUMN business_user_id BIGINT COMMENT '关联商务';
ALTER TABLE biz_customer_balance_record ADD COLUMN business_user_id BIGINT COMMENT '关联商务';
ALTER TABLE biz_card_transaction ADD COLUMN business_user_id BIGINT COMMENT '关联商务';
ALTER TABLE biz_customer_withdraw_order ADD COLUMN business_user_id BIGINT COMMENT '关联商务';

-- changeset hans:10004
-- comment 新增字段
alter table biz_ad_account_order
    add order_method int default 1 not null comment '下户方式(1=授权，2=买断）';


-- changeset yqx:10009
-- comment 新增索引
ALTER TABLE biz_sales_daily_data ADD INDEX idx_create_user (create_user);
ALTER TABLE biz_ad_account_insight ADD INDEX idx_customer_user_id (customer_id, business_user_id);
ALTER TABLE biz_ad_account_order ADD INDEX idx_status_ad_account_bus_user (status, ad_account_id, business_user_id, start_campaign_time, id);
ALTER TABLE biz_ad_account_insight ADD INDEX idx_business_user_id (business_user_id, spend);


-- changeset hans:10005
-- comment 新增字段
alter table biz_customer
    add debt_notify bit default b'0' not null comment '欠款提醒';

-- changeset hans:10006
-- comment 新增投放管理模块表
alter table biz_customer
    add business_type int default 1 not null comment '业务类型';


create table if not exists biz_ad_product
(
    id          bigint                   not null comment 'ID',
    customer_id bigint                   not null comment '客户',
    agent_no    varchar(64)              not null comment '代理线',
    type        int                      not null comment '产品类型',
    excel_head  varchar(1024) default '' not null comment 'excel表头',
    create_time datetime                 not null comment '创建时间',
    create_user bigint                   not null comment '创建人',
    constraint biz_ad_product_pk
        primary key (id)
)
    comment '投放产品';

create table if not exists biz_ad_product_stat
(
    id             bigint                    not null comment 'ID',
    customer_id    bigint                    not null comment '客户',
    product_id     bigint                    not null comment '产品',
    stat_date      date                      null comment '统计日期',
    platform_ad_id varchar(64)               not null comment '广告户',
    actual_spend   decimal(10, 2)            not null comment '实际花费',
    spend          decimal(10, 2)            not null comment '花费',
    effect_num     int                       not null comment '成效数量',
    fee            decimal(10, 2) default 0  not null comment '服务费',
    extra_data     varchar(1024)  default '' not null comment '额外数据',
    create_time    datetime                  not null comment '创建时间',
    create_user    bigint                  not null comment '创建人',
    constraint biz_ad_product_stat_pk
        primary key (id)
)
    comment '产品日报';

-- changeset hans:10007
-- comment 新增字段
alter table biz_ad_product_stat
    add fee_rate decimal(10, 2) not null comment '服务费率' after extra_data;

alter table biz_ad_product_stat
    add reflow_spend decimal(10, 2) not null default 0.00 comment '回流消耗' after fee_rate;

-- changeset hans:10008
-- comment 新增字段
alter table biz_ad_product
    add country varchar(64) default '' not null comment '投放地区' after excel_head;

-- changeset hans:10009
-- comment 新增字段
alter table biz_ad_product_stat
    drop column effect_num;

-- changeset hans:10010
-- comment 新增字段
alter table biz_ad_product
    change excel_head data_template varchar(1024) default '' not null comment '后台数据模板';

alter table biz_ad_product
    add effect_template varchar(255) default '' not null comment '成效数据模板' after country;

create table if not exists biz_ad_product_stat_item
(
    id             bigint                    not null comment 'ID',
    stat_id        bigint                    not null comment '统计数据ID',
    platform_ad_id varchar(64)               not null comment '广告户',
    spend          decimal(10, 2)            not null comment '消耗',
    reflow_spend   decimal(10, 2) default 0  not null comment '回流',
    effect_data    varchar(1024)  default '' not null comment '成效数据',
    create_user    bigint                    not null comment '创建人',
    create_time    datetime                  not null comment '创建时间',
    constraint biz_ad_product_stat_item_pk
        primary key (id)
)
    comment '投放日报明细';

alter table biz_ad_product_stat
    drop column platform_ad_id;

alter table biz_ad_product_stat
    alter column actual_spend set default 0;


-- changeset yyh:10008
-- comment 卡片表增加是否使用
alter table biz_card
    add has_used bit not null default b'0' comment '是否已使用';


-- changeset hans:10011
-- comment 新增字段
alter table biz_ad_product_stat
    add effect_data varchar(1024) default '' not null comment '成效数据' after extra_data;

-- changeset hans:10012
-- comment 新增字段
alter table biz_ad_product
    drop column data_template;

alter table biz_ad_product
    drop column effect_template;

alter table biz_customer
    add effect_template varchar(1024) default '' not null comment '成效模板';

alter table biz_customer
    add data_template varchar(1024) default '' not null comment '后台数据模板';


-- changeset hans:10013
-- comment 新增表
create table if not exists biz_profit
(
    id               bigint                  not null comment 'ID',
    ad_platform      int                     not null comment '媒体平台',
    project          int                  not null comment '项目',
    type             bigint                  not null comment '类型',
    amount           decimal(10, 2)          not null comment '交易金额',
    trans_time       datetime                not null comment '交易时间',
    transaction_hash varchar(64)  default '' not null comment '交易哈希',
    remark           varchar(255) default '' not null comment '备注',
    create_time      datetime                not null comment '创建时间',
    create_user      bigint                  not null comment '创建人',
    constraint biz_profit_pk
        primary key (id)
)
    comment '利润表';

create table if not exists biz_profit_type
(
    id          bigint      not null comment 'ID',
    ad_platform      int                     not null comment '媒体平台',
    name        varchar(64) not null comment '名称',
    create_time datetime    not null comment '创建时间',
    create_user bigint      not null comment '创建人',
    constraint biz_profit_type_pk
        primary key (id)
)
    comment '利润类型';

-- changeset hans:10014
-- comment 调整字段

alter table biz_profit
    add transaction_user_id bigint not null comment '交易对象' after type;

alter table biz_profit
    add transaction_user_type int not null comment '交易对象类型（1=卖户客户，2=物料渠道，3=卖户中介，4=投流甲方，5=代投团队，6=物料商城用户）' after transaction_user_id;

alter table biz_profit
    add num bigint not null comment '交易数量' after transaction_user_type;

-- changeset hans:10015
-- comment 新增表
create table if not exists biz_transaction_user
(
    id               bigint      not null comment 'ID',
    transaction_type int         not null comment '交易类型',
    refer_id         bigint      null comment '关联ID',
    name             varchar(64) not null comment '名字',
    create_user      bigint      not null comment '创建人',
    create_time      datetime    not null comment '创建时间',
    constraint biz_transaction_user_pk
        primary key (id)
)
    comment '交易对象';


-- changeset hans:10016
-- comment 新增表
alter table biz_purchase_order
    add ad_platform int not null comment '媒体平台';

alter table biz_purchase_order
    add project int not null comment '项目';

alter table biz_purchase_order
    add open_receive bit default b'1' not null comment '开启验收';


alter table biz_purchase_order
    modify type bigint not null comment '物料类型';

alter table biz_business_manager
    modify type bigint not null comment '类型';

alter table biz_business_manager_item
    modify type bigint not null comment 'BM类型';

alter table biz_ad_account
    modify bm_item_type bigint null comment 'bm类型';

alter table biz_profit_type
    add cat varchar(64) default '' not null comment '分类' after name;


-- changeset yqx:10010
-- comment 新增字段
ALTER TABLE `biz_ad_product_stat`
    ADD COLUMN `bear_cost` decimal(10, 2) NULL COMMENT '承担费用' AFTER `reflow_spend`;

-- changeset lmc:10001
-- comment 新增字段
ALTER TABLE `biz_customer`
    ADD COLUMN `used_user_id` bigint NULL DEFAULT '0' COMMENT '使用人' AFTER `data_template`;


-- changeset yyy:10113
-- comment 新增字段
ALTER TABLE `biz_customer` DROP COLUMN `used_user_id`;
ALTER TABLE `biz_ad_account_order` ADD COLUMN `used_user_id` bigint NOT NULL DEFAULT '0' COMMENT '使用人';

-- changeset yqx:10011
-- comment 新增表
CREATE TABLE `biz_sales_personnel_monthly_data`
(
    `id`                                bigint   NOT NULL COMMENT 'ID',
    `business_user_id`                  bigint   NOT NULL COMMENT '关联商务',
    `entry_time`                        DATE COMMENT '入职时间',
    `job_rank`                          int      NOT NULL COMMENT '岗位类型',
    `sales_date`                        date     NOT NULL COMMENT '统计月份',
    `wechat_friends_count`              int            DEFAULT NULL COMMENT '微信好友数量',
    `telegram_friends_count`            int            DEFAULT NULL COMMENT 'Telegram好友数量',
    `closed_customers_count`            int            DEFAULT NULL COMMENT '成交客户数量',
    `friends_count`                     int            DEFAULT NULL COMMENT '交友数量',
    `intended_customers_count`          int            DEFAULT NULL COMMENT '意向客户数量',
    `total_customer_spend`              decimal(10, 2) DEFAULT NULL COMMENT '客户总消耗',
    `ad_account_usage_rate`             double         DEFAULT NULL COMMENT '广告户使用率',
    `avg_single_account_spend`          decimal(10, 2) DEFAULT NULL COMMENT '客户单户平均消耗',
    `customer_retention_rate`           double         DEFAULT NULL COMMENT '客户留存率',
    `daily_report_missing_count`        int            DEFAULT NULL COMMENT '日报漏填写数量',
    `customer_consulting_missing_count` int            DEFAULT NULL COMMENT '客咨漏填写数量',
    `comprehensive_evaluation`          int            DEFAULT NULL COMMENT '综合评价',
    `work_attitude`                     int            DEFAULT NULL COMMENT '工作态度',
    `moments_daily_update_count`        int            DEFAULT NULL COMMENT '工作态度',
    `entered_tg_groups_count`           int            DEFAULT NULL COMMENT '进入TG群数量',
    `tg_group_promotion`                int            DEFAULT NULL COMMENT 'TG群推广',
    `share_exchange`                    int            DEFAULT NULL COMMENT '分享交流',
    `create_time`                       datetime NOT NULL COMMENT '创建时间',
    `create_user`                       bigint   NOT NULL COMMENT '创建人',
    `update_time`                       datetime       DEFAULT NULL COMMENT '更新时间',
    `update_user`                       bigint         DEFAULT NULL COMMENT '更新人',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_business_user_id_sales_date` (`business_user_id`, `sales_date`)
) ENGINE = InnoDB COMMENT ='商务人员月度绩效数据';
-- changeset yqx:10012
-- comment 新增表
CREATE TABLE `biz_sales_personnel_config`
(
    `id`               bigint   NOT NULL COMMENT 'ID',
    `business_user_id` bigint   NOT NULL COMMENT '关联商务',
    `sales_date`       date     NOT NULL COMMENT '统计月份',
    `data`             text     not null comment '配置数据',
    `create_time`      datetime NOT NULL COMMENT '创建时间',
    `create_user`      bigint   NOT NULL COMMENT '创建人',
    `update_time`      datetime DEFAULT NULL COMMENT '更新时间',
    `update_user`      bigint   DEFAULT NULL COMMENT '更新人',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_business_user_id_sales_date` (`business_user_id`, `sales_date`)
) ENGINE = InnoDB COMMENT ='商务人员配置数据';
-- changeset yqx:10013
-- comment 修改字段
ALTER TABLE `biz_sales_personnel_monthly_data`
    MODIFY COLUMN `sales_date` date NULL COMMENT '统计月份' AFTER `job_rank`;

-- changeset yqx:10014
-- comment 新增字段
ALTER TABLE `biz_profit`
    ADD COLUMN `related_type` int NOT NULL DEFAULT 0 COMMENT '关联类型 1表示采购订单，0表示无关联' AFTER `create_user`,
    ADD COLUMN `related_Id` bigint NOT NULL DEFAULT 0 COMMENT '关联ID' AFTER `related_type`;

-- changeset lmc:10002
-- comment 新增最近合作时间
ALTER TABLE `biz_customer`
    MODIFY COLUMN `cooperate_time` datetime NULL DEFAULT NULL COMMENT '初次合作时间' AFTER `password`,
    ADD COLUMN `last_cooperate_time` datetime NULL COMMENT '最近合作时间' AFTER `cooperate_time`;

-- changeset yqx:10015
-- comment 新增表
CREATE TABLE `biz_card_bin`
(
    `id`          bigint       NOT NULL COMMENT 'ID',
    `card_bin`    varchar(64)  NOT NULL COMMENT '卡号',
    `platform`    int          NOT NULL COMMENT '所属平台',
    `name`        varchar(255) NOT NULL DEFAULT '' COMMENT '卡头名称',
    `card_scheme` varchar(255) NOT NULL DEFAULT '' COMMENT '卡组织',
    `enable`      tinyint(1)   NOT NULL DEFAULT '1' COMMENT '是否启用',
    `create_time` datetime     NULL COMMENT '创建时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_card_bin` (`card_bin`, `platform`)
) ENGINE = InnoDB COMMENT ='卡头';
-- changeset yqx:10016
-- comment 新增字段
ALTER TABLE `biz_card`
    ADD COLUMN `bin_id` bigint NOT NULL DEFAULT '0' COMMENT '卡头ID' ;
CREATE INDEX idx_biz_card_bin_id ON biz_card (bin_id);

-- changeset lmc:10003
-- comment 下户订单新增余额提醒金额阈值
ALTER TABLE `biz_ad_account_order`
    ADD COLUMN `balance_alert_threshold` decimal NOT NULL DEFAULT -1 COMMENT '余额提醒金额阈值' AFTER `order_method`;

-- changeset hans:10017
-- comment 新增余额提醒是否已提醒
alter table biz_ad_account_order
    add has_balance_alert bit default b'0' not null comment '是否已提醒' after balance_alert_threshold;

-- changeset yqx:10017
-- comment 新增表
CREATE TABLE `biz_channel_spend_daily_stat`
(
    `id`                    BIGINT         NOT NULL COMMENT '主键ID',
    `stat_date`             date           NOT NULL COMMENT '统计日期',
    `channel_id`            BIGINT         NOT NULL COMMENT '渠道ID',
    `bm_type`               bigint            NOT NULL COMMENT 'BM类型',
    `customer_id`           BIGINT                  DEFAULT NULL COMMENT '客户ID',
    `ad_account_id`         VARCHAR(100)   NOT NULL COMMENT '广告户ID',
    `spend_amount`          DECIMAL(10, 2) NOT NULL DEFAULT '0.00' COMMENT '消耗金额',
    `customer_service_rate` DECIMAL(10, 2) NOT NULL DEFAULT '0.00' COMMENT '客户服务费率',
    `channel_service_rate`  DECIMAL(10, 2) NOT NULL DEFAULT '0.00' COMMENT '渠道服务费率',
    `create_time`           datetime       NOT NULL COMMENT '创建时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY uk_stat_channel_bm_customer_ad_account (stat_date, channel_Id, bm_type, customer_id, ad_account_id)
) ENGINE = INNODB COMMENT = '每日渠道统计';
-- changeset lmc:10004
-- comment 新增渠道合作政策表 新增菜单
CREATE TABLE `biz_cooperation_policy`  (
     `id` bigint NOT NULL COMMENT '主键id',
     `channel_id` bigint NOT NULL COMMENT '渠道ID',
     `bm_type` bigint NOT NULL COMMENT 'BM类型',
     `account_open_fee` decimal(8, 2) NOT NULL DEFAULT 0.00 COMMENT '开户费，两位小数',
     `service_fee_rate_percent` decimal(8, 2) NOT NULL DEFAULT 0.00 COMMENT '服务费率，百分比，两位小数',
     `deposit_return_threshold_amount` decimal(8, 2) NULL DEFAULT NULL COMMENT '消耗返押金的达标金额，金额，两位小数',
     `remark` varchar(255)  DEFAULT '' COMMENT '备注',
     `create_time` datetime NOT NULL COMMENT '创建时间',
     `create_user` bigint NOT NULL COMMENT '创建人',
     `update_time` datetime NULL DEFAULT NULL COMMENT '更新时间',
     `update_user` bigint NULL DEFAULT NULL COMMENT '更新人',
     PRIMARY KEY (`id`) USING BTREE,
     UNIQUE INDEX `uniq_channel_bmtype`(`channel_id`, `bm_type`) USING BTREE
) ENGINE = InnoDB  COMMENT = '渠道合作政策表' ;

-- changeset lmc:10005
-- comment 新增用户拓展表
CREATE TABLE `biz_customer_extra`  (
    `id` bigint NOT NULL COMMENT 'ID',
    `customer_id` bigint NOT NULL COMMENT '关联客户',
    `balance_alert_threshold` decimal(10, 2) NOT NULL DEFAULT -1.00 COMMENT '余额提醒金额阈值',
    `create_user` bigint NOT NULL COMMENT '创建人',
    `create_time` datetime NOT NULL COMMENT '创建时间',
    `update_time` datetime NULL DEFAULT NULL COMMENT '更新时间',
    `update_user` bigint NULL DEFAULT NULL COMMENT '更新人',
    PRIMARY KEY (`id`) USING BTREE,
    UNIQUE KEY `uk_customer_id` (`customer_id`)
) ENGINE = InnoDB COMMENT = '客户拓展表';
