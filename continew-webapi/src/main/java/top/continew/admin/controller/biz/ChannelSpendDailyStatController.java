package top.continew.admin.controller.biz;

import cn.dev33.satoken.annotation.SaIgnore;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RestController;
import top.continew.admin.biz.model.query.ChannelAdAccountStatQuery;
import top.continew.admin.biz.model.query.ChannelSpendDailyStatQuery;
import top.continew.admin.biz.model.query.ChannelSummaryStatQuery;
import top.continew.admin.biz.model.req.ChannelSpendDailyStatReq;
import top.continew.admin.biz.model.resp.ChannelAdAccountStatResp;
import top.continew.admin.biz.model.resp.ChannelSpendDailyStatDetailResp;
import top.continew.admin.biz.model.resp.ChannelSpendDailyStatResp;
import top.continew.admin.biz.model.resp.ChannelSummaryStatResp;
import top.continew.admin.biz.service.ChannelSpendDailyStatService;
import top.continew.admin.common.base.BaseController;
import top.continew.starter.extension.crud.annotation.CrudRequestMapping;
import top.continew.starter.extension.crud.model.query.PageQuery;
import top.continew.starter.extension.crud.model.resp.PageResp;
import top.continew.starter.log.annotation.Log;

/**
 * 每日渠道统计 API
 *
 * <AUTHOR>
 * @since 2025/09/08 15:55
 */
@Tag(name = "每日渠道统计 API")
@RestController
@CrudRequestMapping(value = "/biz/channelSpendDailyStat", api = {})
public class ChannelSpendDailyStatController extends BaseController<ChannelSpendDailyStatService, ChannelSpendDailyStatResp, ChannelSpendDailyStatDetailResp, ChannelSpendDailyStatQuery, ChannelSpendDailyStatReq> {


    @GetMapping("/summaryStatPage")
    @Operation(summary = "汇总统计分页列表")
    @Log(ignore = true)
    public PageResp<ChannelSummaryStatResp> summaryStatPage(ChannelSummaryStatQuery query, PageQuery pageQuery) {
        return baseService.summaryStatPage(query, pageQuery);
    }

    @GetMapping("/summaryStat")
    @Operation(summary = "汇总统计")
    @Log(ignore = true)
    public ChannelSummaryStatResp summaryStat(ChannelSummaryStatQuery query) {
        return baseService.summaryStat(query);
    }

    @Operation(summary = "导出汇总统计", description = "导出汇总统计")
    @GetMapping("/summaryStat/export")
    public void exportSummaryStat(ChannelSummaryStatQuery query, HttpServletResponse response) {
        baseService.exportSummaryStat(query, response);
    }


    @GetMapping("/adAccountStatPage")
    @Operation(summary = "广告户统计分页列表")
    @Log(ignore = true)
    public PageResp<ChannelAdAccountStatResp> adAccountStatPage(ChannelAdAccountStatQuery query, PageQuery pageQuery) {
        return baseService.adAccountStatPage(query, pageQuery);
    }

    @Operation(summary = "导出广告户统计", description = "导出广告户统计")
    @GetMapping("/adAccountStat/export")
    public void exportAdAccountStat(ChannelAdAccountStatQuery query, HttpServletResponse response) {
        baseService.exportAdAccountStat(query, response);
    }
}