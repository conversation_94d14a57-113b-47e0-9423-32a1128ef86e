<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="top.continew.admin.biz.mapper.ChannelSpendDailyStatMapper">
    <sql id="selectSummaryStatBase">
        WITH openAccount AS (SELECT type            AS bmType,
                                    channel_id      AS channelId,
                                    SUM(unit_price) AS totalUnitPrice
                             FROM biz_business_manager_item
                                <where>
                                    <if test="query.startTime != null and query.endTime != null">
                                        create_time BETWEEN #{query.statTime} AND #{query.endTime}
                                    </if>
                                </where>
                             GROUP BY channel_id,`type`),
             customerOpeningFeePerChannelBm AS (SELECT bmi.channel_id      AS channelId,
                                                       bmi.type            AS bmType,
                                                       SUM(bao.pay_amount) AS totalCustomerOpeningFee
                                                FROM biz_ad_account_order bao
                                                         JOIN biz_business_manager_item bmi
                                                              ON bao.ad_account_id = bmi.platform_ad_id
                                                <where>
                                                    bao.status IN (3,5)
                                                    <if test="query.startTime != null and query.endTime != null">
                                                        AND bao.finish_time BETWEEN #{query.statTime} AND #{query.endTime}
                                                    </if>
                                                </where>
                                                GROUP BY bmi.channel_id,
                                                         bmi.type)
        SELECT bbmc.NAME                                             AS channelName,
               bpt.NAME                                              AS bmType,
               sum(bcsds.spend_amount)                               AS totalSpendAmount,
               sum(bcsds.spend_amount * bcsds.channel_service_rate)  AS channelServiceFeeExpense,
               COALESCE(
                       (SELECT SUM(oa_sub.totalUnitPrice)
                        FROM openAccount oa_sub
                        WHERE oa_sub.channelId = bcsds.channel_id
                          AND oa_sub.bmType = bcsds.bm_type),
                       0
               )                                                     AS channelAccountOpeningFeeExpense,
               sum(bcsds.spend_amount * bcsds.customer_service_rate) AS customerServiceFeeIncome,
               -- 客户收入-开户费
               COALESCE(cof_per_cb.totalCustomerOpeningFee, 0)       AS customerAccountOpeningFeeIncome
        FROM biz_channel_spend_daily_stat bcsds
                 LEFT JOIN biz_business_manager_channel bbmc ON bbmc.id = bcsds.channel_id
                 LEFT JOIN biz_profit_type bpt ON bpt.id = bcsds.bm_type
                 LEFT JOIN customerOpeningFeePerChannelBm cof_per_cb
                           ON cof_per_cb.channelId = bcsds.channel_id
                               AND cof_per_cb.bmType = bcsds.bm_type
        <where>
            <if test="query.channelIds != null and query.channelIds.size() > 0">
                AND bcsds.channel_id IN
                <foreach collection="query.channelIds" item="id" open="(" separator="," close=")">
                    #{id}
                </foreach>
            </if>
            <if test="query.bmTypes != null and query.bmTypes.size() > 0">
                AND bcsds.bm_type IN
                <foreach collection="query.bmTypes" item="bmType" open="(" separator="," close=")">
                    #{bmType}
                </foreach>
            </if>
            <if test="query.startTime != null and query.endTime != null">
                AND bcsds.stat_date BETWEEN #{query.statTime} AND #{query.endTime}
            </if>
        </where>
        GROUP BY bcsds.channel_id,
                 bcsds.bm_type
        ORDER BY bcsds.channel_id, bcsds.bm_type
    </sql>
    <select id="selectSummaryStatPage" resultType="top.continew.admin.biz.model.resp.ChannelSummaryStatResp">
        <include refid="selectSummaryStatBase"/>
    </select>
    <select id="selectSummaryStatList" resultType="top.continew.admin.biz.model.resp.ChannelSummaryStatResp">
        <include refid="selectSummaryStatBase"/>
    </select>

    <sql id="selectAdAccountStatBase">
        SELECT bbmc.NAME               AS channelName,
               bpt.NAME                AS bmType,
               Min(bbmi.create_time)   AS accountAcquisitionTime,
               baa.account_status      AS adAccountStatus,
               bcsds.ad_account_id     AS adAccountId,
               SUM(bcsds.spend_amount) AS totalSpendAmount
        FROM biz_channel_spend_daily_stat bcsds
                 LEFT JOIN biz_business_manager_channel bbmc ON bbmc.id = bcsds.channel_id
                 LEFT JOIN biz_profit_type bpt ON bpt.id = bcsds.bm_type
                 LEFT JOIN biz_ad_account baa ON baa.platform_ad_id = bcsds.ad_account_id
                 LEFT JOIN biz_business_manager_item bbmi ON bbmi.type = bcsds.bm_type
            AND bbmi.platform_ad_id = bcsds.ad_account_id
            AND bbmi.channel_id = bcsds.channel_id
        WHERE bcsds.stat_date &lt; #{statDate}
        <if test="adAccountIds != null and !adAccountIds.isEmpty()">
            AND bcsds.ad_account_id IN
            <foreach collection="adAccountIds" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        GROUP BY bcsds.ad_account_id,
                 bcsds.channel_id,
                 bcsds.bm_type
    </sql>

    <select id="selectAdAccountStatPage"
            resultType="top.continew.admin.biz.model.resp.ChannelAdAccountStatResp">
        <include refid="selectAdAccountStatBase"/>
    </select>
    <select id="selectAdAccountStatList"
            resultType="top.continew.admin.biz.model.resp.ChannelAdAccountStatResp">
        <include refid="selectAdAccountStatBase"/>
    </select>

</mapper>
