package top.continew.admin.biz.mapper;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Param;
import top.continew.admin.biz.model.entity.ChannelSpendDailyStatDO;
import top.continew.admin.biz.model.query.ChannelAdAccountStatQuery;
import top.continew.admin.biz.model.query.ChannelSummaryStatQuery;
import top.continew.admin.biz.model.resp.ChannelAdAccountStatResp;
import top.continew.admin.biz.model.resp.ChannelSummaryStatResp;
import top.continew.starter.data.mp.base.BaseMapper;

import java.time.LocalDate;
import java.util.List;

/**
 * 每日渠道统计 Mapper
 *
 * <AUTHOR>
 * @since 2025/09/08 15:55
 */
public interface ChannelSpendDailyStatMapper extends BaseMapper<ChannelSpendDailyStatDO> {

    IPage<ChannelSummaryStatResp> selectSummaryStatPage(@Param("page") Page<Object> objectPage, @Param("query") ChannelSummaryStatQuery query);

    List<ChannelSummaryStatResp> selectSummaryStatList(@Param("query") ChannelSummaryStatQuery query);

    IPage<ChannelAdAccountStatResp> selectAdAccountStatPage(@Param("page") Page<Object> objectPage,@Param("statDate") LocalDate statDate,@Param("adAccountIds") List<String> adAccountIds);

    List<ChannelAdAccountStatResp> selectAdAccountStatList(@Param("statDate") LocalDate statDate,@Param("adAccountIds") List<String> adAccountIds);
}