package top.continew.admin.biz.model.resp;

import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;

@Data
public class ChannelSummaryStatResp implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;


    @Schema(description = "渠道名称")
    @ExcelProperty(value = "渠道名称")
    private String channelName;

    @Schema(description = "BM类型")
    @ExcelProperty(value = "BM类型")
    private String bmType;

    @Schema(description = "总消耗金额")
    @ExcelProperty(value = "总消耗金额")
    private BigDecimal totalSpendAmount;

    @Schema(description = "渠道支出 - 服务费")
    @ExcelProperty(value = "渠道支出 - 服务费")
    private BigDecimal channelServiceFeeExpense;

    @Schema(description = "渠道支出 - 开户费")
    @ExcelProperty(value = "渠道支出 - 开户费")
    private BigDecimal channelAccountOpeningFeeExpense;

    @Schema(description = "客户收入 - 服务费")
    @ExcelProperty(value = "客户收入 - 服务费")
    private BigDecimal customerServiceFeeIncome;

    @Schema(description = "客户收入 - 开户费")
    @ExcelProperty(value = "客户收入 - 开户费")
    private BigDecimal customerAccountOpeningFeeIncome;

    @Schema(description = "利润")
    @ExcelProperty(value = "利润")
    private BigDecimal profit;
}