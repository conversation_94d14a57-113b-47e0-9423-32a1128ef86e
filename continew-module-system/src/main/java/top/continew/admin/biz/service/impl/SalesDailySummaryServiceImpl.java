package top.continew.admin.biz.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import top.continew.admin.biz.mapper.SalesDailySummaryMapper;
import top.continew.admin.biz.model.entity.SalesDailySummaryDO;
import top.continew.admin.biz.model.query.SalesDailySummaryQuery;
import top.continew.admin.biz.model.req.BatchUpdateSalesDailySummaryReq;
import top.continew.admin.biz.model.req.SalesDailySummaryReq;
import top.continew.admin.biz.model.resp.SalesDailySummaryDetailResp;
import top.continew.admin.biz.model.resp.SalesDailySummaryResp;
import top.continew.admin.biz.service.SalesDailySummaryService;
import top.continew.admin.system.model.resp.user.UserDetailResp;
import top.continew.admin.system.service.UserService;
import top.continew.starter.core.exception.BusinessException;
import top.continew.starter.core.validation.CheckUtils;
import top.continew.starter.extension.crud.model.query.PageQuery;
import top.continew.starter.extension.crud.model.query.SortQuery;
import top.continew.starter.extension.crud.model.resp.PageResp;
import top.continew.starter.extension.crud.service.BaseServiceImpl;

import java.time.LocalDate;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 商务日报业务实现
 *
 * <AUTHOR>
 * @since 2025/08/01 14:29
 */
@Service
@RequiredArgsConstructor
public class SalesDailySummaryServiceImpl extends BaseServiceImpl<SalesDailySummaryMapper, SalesDailySummaryDO, SalesDailySummaryResp, SalesDailySummaryDetailResp, SalesDailySummaryQuery, SalesDailySummaryReq> implements SalesDailySummaryService {

    private final UserService userService;

    @Override
    public void telegramSave(SalesDailySummaryDO salesDailySummaryDO) {

        SalesDailySummaryDO salesDailySummary = lambdaQuery().eq(SalesDailySummaryDO::getCreateUser, salesDailySummaryDO.getCreateUser())
                .eq(SalesDailySummaryDO::getRecordDate, salesDailySummaryDO.getRecordDate())
                .one();
        if (salesDailySummary != null) {
            // 存在旧记录，设置ID进行更新
            salesDailySummaryDO.setId(salesDailySummary.getId());
        }
        saveOrUpdate(salesDailySummaryDO);

    }

    @Transactional
    @Override
    public void batchUpdateSalesDailySummaryCreateUser(BatchUpdateSalesDailySummaryReq req) {
        UserDetailResp user = userService.get(req.getCreateUser());
        CheckUtils.throwIfNull(user, "商务不存在");
        // 批量更新
        this.update(Wrappers.<SalesDailySummaryDO>lambdaUpdate().set(SalesDailySummaryDO::getCreateUser, req.getCreateUser())
                .in(SalesDailySummaryDO::getId, req.getIds()));
    }

    @Override
    protected void beforeAdd(SalesDailySummaryReq req) {
        if (exists(new LambdaQueryWrapper<SalesDailySummaryDO>()
                .eq(SalesDailySummaryDO::getRecordDate, req.getRecordDate())
                .eq(SalesDailySummaryDO::getCreateUser, req.getCreateUser()))) {
            throw new BusinessException("已存在日报");
        }
    }
}